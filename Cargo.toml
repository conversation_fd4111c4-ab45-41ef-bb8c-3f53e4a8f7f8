[package]
name = "axum_study"
version = "0.1.0"
edition = "2024"

[dependencies]
axum = { version = "0.8.4", features = ["macros"] }
tokio = { version = "1.45.1", features = ["full"] }
tracing = { version = "0.1.41", features = ["async-await"] }
tracing-subscriber = { version = "0.3.19", features = ["env-filter", "chrono"] }
config = { version = "0.15.11", features = ["yaml"] }
serde = { version = "1.0.219", features = ["derive"] }
anyhow = "1.0.98"
sea-orm = { version = "1.1.12", features = ["with-chrono", "debug-print", "sqlx-postgres", "with-rust_decimal", "runtime-tokio"
] }
num_cpus = "1.17.0"
thiserror = "2.0.12"
tower-http = { version = "0.6.6", features = ["trace", "timeout", "limit", "cors", "normalize-path", "auth"] }
xid = "1.1.1"
bytesize = "2.0.1"
validator = { version = "0.20.0", features = ["derive"] }
axum-valid = { version = "0.23.0", features = ["full_validator"] }
regex = "1.11.1"
idgenerator = "2.0.0"
bcrypt = "0.17.0"
jsonwebtoken = "9.3.1"